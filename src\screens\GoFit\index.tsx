import React, {useState} from 'react';
import {FlatList, View, TouchableOpacity, ImageBackground} from 'react-native';
import {styles as createStyles} from './styles';
import {useThemeStore} from '@/store/themeStore';
import {Header, Icon, NoData, SafeAreaView} from '@/components';
import {useNavigation} from '@react-navigation/native';
import {StackNavigationProp} from '@react-navigation/stack';
import {CommunityStackParamList} from '@/navigation/CommunityStack';
import Tabs from '@/components/Tabs';
import CommunityCard from '@/components/CommunityCard';
import Typography from '@/components/Typography';
import {Images} from '@/config';
import MediaCard from '@/components/MediaCard';
import {useMutation, useQuery, useQueryClient} from '@tanstack/react-query';
import api from '@/services/api';
import {getCommunityData, setupCommunityMutation} from '@/utils/commonFunctions';
import VideoActionsModal from '@/components/VideoActionsModal';
import CLoader from '@/components/CLoader';
import RefreshControl from '@/components/RefreshControl';

type NavigationProp = StackNavigationProp<CommunityStackParamList>;

interface GoFitData {
  id: string;
  title: string;
  description: string;
  image: string;
  file_thumbnail: string;
  tags: GoFitTag[];
  buttonText?: string;
  is_liked: boolean;
  likes_count: number;
  [key: string]: any;
}

interface GoFitTag {
  id: number;
  name: string;
  title: string;
}

// Update the API response type to be an array of GoFitData
type ApiResponse = GoFitData[];

interface VideoActionsModalState {
  visible: boolean;
  GoFitData: GoFitData | null;
}
const GoFit = () => {
  const theme = useThemeStore();
  const styles = createStyles(theme);

  const navigation = useNavigation<NavigationProp>();

  const goBack = () => {
    navigation.goBack();
  };

  const tabs = ['Fitness', 'Strength', 'Health'];

  const [activeTab, setActiveTab] = useState('Fitness');
  const [isVideoActionsModalVisible, setIsVideoActionsModalVisible] =
    useState<VideoActionsModalState>({
      visible: false,
      GoFitData: null,
    });
  const queryClient = useQueryClient();

  const handleTabPress = (tab: string) => {
    setActiveTab(tab);
  };

  const {
    data: GoFitResponse,
    isLoading,
    refetch,
  } = useQuery<ApiResponse>({
    queryKey: ['go-fit', activeTab],
    queryFn: async () => {
      const response = await getCommunityData('go-fit', activeTab);
      return response as ApiResponse;
    },
  });

  // Extract the data array from the API response
  const GoFitData: GoFitData[] = GoFitResponse || [];

  const updateVideoModalState = (id: string, isLiked: boolean) => {
    // Update modal data if it's the same item
    if (isVideoActionsModalVisible.GoFitData?.id === id) {
      setIsVideoActionsModalVisible(prev => ({
        ...prev,
        GoFitData: prev.GoFitData
          ? {
              ...prev.GoFitData,
              is_liked: isLiked,
              likes_count: isLiked
                ? (prev.GoFitData.likes_count || 0) + 1
                : (prev.GoFitData.likes_count || 0) - 1,
            }
          : null,
      }));
    }
  };

  const {mutate: updateLike} = useMutation(
    setupCommunityMutation<GoFitData>(queryClient, ['go-fit'], updateVideoModalState),
  );

  const handleLikePress = (item: GoFitData) => {
    updateLike({
      id: item.id,
      is_liked: !item.is_liked,
    });
  };

  const renderComponent = ({item}: {item: GoFitData}) => {
    return (
      <View>
        <CommunityCard
          data={item}
          onCardPress={() => {
            navigation.navigate('CommunityDetails', {
              from: 'GoFit',
              title: item.title,
              data: item,
            });
          }}
          onLikePress={() => {}}
          onCommentPress={() => {}}
          onSharePress={() => {}}
          containerStyle={styles.cardContainer}
          variant="tag"
          showMore={false}
          onMoreIconPress={() => {
            setIsVideoActionsModalVisible({
              visible: true,
              GoFitData: item,
            });
          }}
        />
        {item.buttonText && (
          <TouchableOpacity style={styles.blueButton}>
            <Typography variant="communityBtnText" color={theme.colors.white}>
              {item.buttonText}
            </Typography>
          </TouchableOpacity>
        )}
      </View>
    );
  };

  const renderHeader = () => (
    <View style={styles.headerContainer}>
      <MediaCard source={Images.goFitImage} imageContainerStyle={styles.imageContainer} />
      <View style={styles.tabContainer}>
        <Tabs
          variant="square"
          tabs={tabs}
          activeTab={activeTab}
          onTabPress={handleTabPress}
          tabStyle={styles.tabStyle}
          tabTitleStyle={styles.tabTitleStyle}
          type="inner"
          listContainerStyle={styles.listContainerStyle}
        />
      </View>
    </View>
  );

  return (
    <ImageBackground source={Images.gradientBg} style={styles.backgroundImage}>
      <SafeAreaView includeBottom={true} style={styles.root}>
        <Header
          showBack={false}
          leftComponent={
            <TouchableOpacity onPress={() => navigation.goBack()}>
              <Icon name="Left-chevron" size={24} color={theme.colors.white} />
            </TouchableOpacity>
          }
          title="GoFit"
          // pageTitle="GoFit"
          backgroundColor="transparent"
          isBackPress={true}
          backNavigation={goBack}
          rightIcons={[
            {name: 'notification', size: 24, badge: 0},
            {name: 'chat', size: 24, badge: 14},
          ]}
        />
        {isLoading ? (
          <CLoader />
        ) : (
          <FlatList
            data={GoFitData}
            showsVerticalScrollIndicator={false}
            keyExtractor={(item, index) => index.toString()}
            renderItem={renderComponent}
            contentContainerStyle={styles.content}
            onEndReachedThreshold={0.5}
            ListHeaderComponent={renderHeader}
            ListEmptyComponent={
              <NoData
                title={`No ${activeTab} Data`}
                message={`No ${activeTab.toLowerCase()} content available at the moment.`}
              />
            }
            refreshControl={<RefreshControl refreshing={isLoading} onRefresh={() => refetch()} />}
          />
        )}
      </SafeAreaView>
      <VideoActionsModal
        visible={isVideoActionsModalVisible.visible}
        onClose={() =>
          setIsVideoActionsModalVisible({
            visible: false,
            GoFitData: null,
          })
        }
        data={isVideoActionsModalVisible.GoFitData}
        videoTitle="Latest Reviews"
        isLiked={isVideoActionsModalVisible.GoFitData?.is_liked ?? false}
        videoThumbnail="https://picsum.photos/200/300"
        onLike={() => {
          if (isVideoActionsModalVisible.GoFitData) {
            handleLikePress(isVideoActionsModalVisible.GoFitData);
          }
        }}
        onComment={() => {
          navigation.navigate('CommentScreen', {
            from: 'Latest Reviews',
            title: 'Latest Reviews',
            data: GoFitData?.[0],
          });
          setIsVideoActionsModalVisible({
            visible: false,
            GoFitData: null,
          });
        }}
        onShare={() => {}}
      />
    </ImageBackground>
  );
};

export default GoFit;
