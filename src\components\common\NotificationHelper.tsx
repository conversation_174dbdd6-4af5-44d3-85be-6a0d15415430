// import PushNotification from ;
import { Platform } from 'react-native';
import PushNotification from 'react-native-push-notification';
import PushNotificationIOS from '@react-native-community/push-notification-ios';
import { getBatchCount } from '@app/utils/CommonFunction';

// const PushNotification = require('react-native-push-notification');
/**
 *
 *@function showNotification
 *
 */
export const showNotification = (remoteMessage: any, isForeground = false) => {
  console.log('Show Notification Called', remoteMessage);
  setTimeout(() => {
    getBatchCount()
  }, 6000);
  // store.dispatch(NotificationAction.setNotificationType(remoteMessage));
  const { data, notification } = remoteMessage;
  // sendLocalNotification(data.title, data.message);
  if (
    !remoteMessage.hasOwnProperty('notification') /*notification === undefined*/
  ) {
    // For Handling Comet Chat Notifications
    console.log('No Notification key present');
    const { title, alert, message } = data;
    let notificationTitle = title;
    console.log('title>>>', title);
    console.log('alert>>>', alert);
    console.log('message>>>', message ? JSON.stringify(message) : '');
    if (message) {
      sendLocalNotification(notificationTitle, alert, message);
    }
  } else {
    const { source } = data;
    console.log('Notification key present');
    // if (isForeground) {
    //Receiving Notification from our server in foreground
    const { body, title } = notification;
    console.log('title, body, data remoteMessage ===>', remoteMessage);
    sendLocalNotification(title, body, data);

    // }
  }
};

const sendLocalNotification = (title: any, body: any, data: any) => {
  if (Platform.OS === 'android') {
    try {
      // Ensure the channel is created before sending notifications
      PushNotification.createChannel(
        {
          channelId: '1', // (required)
          channelName: 'My channel', // (required)
          channelDescription: 'A channel to categorize your notifications', // (optional)
          sound: 'default', // (optional) default: 'default'
          // importance: PushNotification.Importance.HIGH, // (optional) default: Importance.HIGH
          vibrate: true, // (optional) default: true
        },
        created =>
          console.log(`createChannel returned '${created}'`, title, body), // (optional) callback
      );

      PushNotification.localNotification({
        channelId: '1', // (required)
        title: title,
        message: body,
      });
    } catch (e) {
      console.log('e ===>', e);
    }
  } else {
    let details = {
      alertTitle: title,
      alertBody: body,
      userInfo: data,
    };

    console.log('PushNotificationIOS ===', PushNotificationIOS);
    PushNotificationIOS.presentLocalNotification(details);
  }
};
