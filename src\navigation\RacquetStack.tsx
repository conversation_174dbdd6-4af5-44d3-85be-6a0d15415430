import React from 'react';
import {createStackNavigator} from '@react-navigation/stack';
import {RacquetsScreen} from '@/screens/tabs';
import RacquetCategoryScreen from '@/screens/tabs/RacquetsScreens/RacqetsCategory';
import RacquetsBrandsScreen from '@/screens/tabs/RacquetsScreens/Brands';
import RacquetSelector from '@/screens/tabs/RacquetsScreens/RacquetSelector';
import RacquetSelectorDetail from '@/screens/tabs/RacquetsScreens/RacquetSelectorDetail';

export type RacquetStackParamList = {
  RacquetsScreen: undefined;
  RacquetCategory: {
    sportsTitle: string;
  };
  RacquetBrands: {
    sportsTitle: string;
    category: string;
  };
  RacquetSelector: undefined;
  RacquetSelectorDetail: undefined;
};

const Stack = createStackNavigator<RacquetStackParamList>();

const RacquetStack = () => {
  return (
    <Stack.Navigator
      initialRouteName="RacquetsScreen"
      screenOptions={{
        headerShown: false,
        gestureEnabled: true, // Enable gesture navigation
      }}>
      <Stack.Screen
        name="RacquetsScreen"
        component={RacquetsScreen}
        options={{gestureEnabled: true, animation: 'fade', presentation: 'transparentModal'}}
      />
      <Stack.Screen
        name="RacquetCategory"
        component={RacquetCategoryScreen}
        options={{gestureEnabled: true, animation: 'fade', presentation: 'transparentModal'}}
      />
      <Stack.Screen
        name="RacquetBrands"
        component={RacquetsBrandsScreen}
        options={{gestureEnabled: true, animation: 'fade', presentation: 'transparentModal'}}
      />
      <Stack.Screen
        name="RacquetSelector"
        component={RacquetSelector}
        options={{gestureEnabled: true, animation: 'fade', presentation: 'transparentModal'}}
      />
      <Stack.Screen
        name="RacquetSelectorDetail"
        component={RacquetSelectorDetail}
        options={{gestureEnabled: true, animation: 'fade', presentation: 'transparentModal'}}
      />
    </Stack.Navigator>
  );
};

export default RacquetStack;
