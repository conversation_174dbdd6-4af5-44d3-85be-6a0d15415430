import { useEffect } from 'react';
import messaging from '@react-native-firebase/messaging';
import PushNotification from 'react-native-push-notification';
import {store} from '@redux/store/configureStore';
import NotificationAction from '@redux/reducers/notification/actions';
import AuthActionsAction from '@redux/reducers/auth/actions';
import UserConfigActions from '@redux/reducers/userConfig/actions';
import {handleNavigationOnNotification} from '@app/utils/CommonFunction';
import {showNotification} from './NotificationHelper';
import {PermissionsAndroid, Platform} from 'react-native';
import {isEmpty, isString, isUndefined} from '@app/utils/lodashFactions';
import {EventRegister} from 'react-native-event-listeners';
import {navigationRef} from '@navigation/NavigationService';
import {useSelector} from 'react-redux';

// const PushNotification = require('react-native-push-notification');
// Store FCM token
const storeFCMToken = async (token: any) => {
  const {
    notification: {fcmToken},
  } = store.getState();

  try {
    await store.dispatch(NotificationAction.setFcmToken(token));
  } catch (error) {}
};

export const getToken = async () => {
  try {
    messaging()
      .getToken()
      .then(token => {
        console.log('FCM Token', token);
        return storeFCMToken(token);
      });
  } catch (e) {
    console.log('e ===>', e);
  }
};

// Delete fcm token
export const deleteFcmToken = () => {
  try {
    messaging()
      .deleteToken()
      .then(res => {
        console.log('deleteToken FCM Token', res);
        storeFCMToken('');
      });
  } catch (e) {
    console.log('e ====>', e);
  }
};

/**
 *firebase notification
 * @function  RemotePushController
 */
const RemotePushNotification = () => {
  const {
    auth: { userData, accessToken },
  } = store.getState();

  const { socket } = store.getState();
  const { selectedRoom } = useSelector((s: any) => s.socket);
  const requestNotificationPermission = async () => {
    if (Platform.OS === 'ios') {
      const authStatus = await messaging().requestPermission();

      // Request permissions
      return (
        authStatus === messaging.AuthorizationStatus.AUTHORIZED ||
        authStatus === messaging.AuthorizationStatus.PROVISIONAL
      );
    } else if (Platform.OS === 'android') {
      const granted = await PermissionsAndroid.request(
        PermissionsAndroid.PERMISSIONS.POST_NOTIFICATIONS,
      );
      return granted === PermissionsAndroid.RESULTS.GRANTED;
    }
    return false;
  };

  useEffect(() => {
    const requestPermissions = async () => {
      const hasPermission = await requestNotificationPermission();
      if (hasPermission) {
        console.log('Notification permission granted');
      } else {
        console.log('Notification permission denied');
      }
    };

    requestPermissions();
    PushNotification.configure({
      // (required) Called when a remote or local notification is opened or received
      onNotification: function (notification: any) {
        console.log('notification configure ===>', notification);
        // old app code
        // dispatch({ type: 'onNotificationOpen', payload: notification });
        // store.dispatch(NotificationAction.onNotificationOpen(notification));
        // store.dispatch(NotificationAction.setNotificationType(notification));

        //Handle on notification click Events
        if (notification?.userInteraction && notification?.foreground) {
          // callCommonHandleNotificationfunction(notification);
        }
      },

      popInitialNotification: true,
      requestPermissions: true,
    });

    PushNotification.createChannel(
      {
        channelId: '1', // (required)
        channelName: 'harbor', // (required)
        channelDescription: 'harbor default channel', // (optional) default: undefined.
        soundName: 'default', // (optional) See `soundName` parameter of `localNotification` function
        importance: 4, // (optional) default: 4. Int value of the Android notification importance
        vibrate: true, // (optional) default: true. Creates the default vibration patten if true.
      },
      created =>
        console.log(`createChannel 'default-channel-id' returned '${created}'`), // (optional) callback returns whether the channel was created, false means it already existed.
    );

    messaging().onNotificationOpenedApp((remoteMessage: any) => {
      const { data, notification } = remoteMessage;
      const { body, title } = notification;
      console.log('remoteMessage background state: ===>', remoteMessage);
      if (data?.action) {
        store.dispatch(NotificationAction.setNotificationType(data));
        const d =
          typeof data?.meta === 'string' ? JSON.parse(data?.meta) : data?.meta;

        if (d && data?.action === 'chat') {
          // store.dispatch(
          //   NotificationAction.onNotificationOpen({
          //     ...remoteMessage,
          //     userInfo: {...d, selectedTab: d?.receiverType},
          //   }),
          // );
          handleNavigationOnNotification('ChatDetails', {
            userInfo: {
              ...d,
              selectedTab: d?.receiverType,
              canGoBack: false,
            },
          });
        } else if (d?.jobId) {
          handleNavigationOnNotification('JobApplicant', d);
        }

        if (data?.action === 'update_bank_profile') {
          store.dispatch(
            AuthActionsAction.setUserData({
              ...store.getState().auth.userData,
              bankAccountVerified: data?.data?.bankAccountVerified,
            }),
          );
        }
      }
      console.log(
        'Notification caused app to open from background state:',
        remoteMessage,
      );
      // callCommonHandleNotificationfunction(remoteMessage?.notification);
    });
    // Check whether an initial notification is available
    messaging()
      .getInitialNotification()
      .then(remoteMessage => {
        if (remoteMessage) {
          console.log(
            'Notification caused app to open from quit state:',
            remoteMessage,
          );

          const { data, notification } = remoteMessage;
          console.log('remoteMessage quit state: ===>', remoteMessage);
          if (data?.action) {
            const d =
              typeof data?.meta === 'string'
                ? JSON.parse(data?.meta)
                : data?.meta;
            const obj = isString(d) ? JSON.parse(d) : d;
            if (d && data?.action === 'chat') {
              // store.dispatch(
              //   NotificationAction.onNotificationOpen({
              //     ...remoteMessage,
              //     userInfo: {...d, selectedTab: d?.receiverType},
              //     canGoBack: false,
              //   }),
              // );
              handleNavigationOnNotification('ChatDetails', {
                userInfo: {
                  ...d,
                  selectedTab: d?.receiverType,
                  canGoBack: false,
                },
              });
            } else if (d?.jobId) {
              console.log(
                'JobApplicant d ===>',
                d,
                isString(d) ? JSON.parse(d) : d,
                isString(d),
                typeof obj,
              );
              handleNavigationOnNotification('JobApplicant', {
                ...obj,
                canGoBack: false,
              });
            }
            if (!isUndefined(obj?.isOccupied)) {
              store.dispatch(
                AuthActionsAction.setUserData({
                  ...store.getState().auth.userData,
                  isOccupied: obj?.isOccupied,
                }),
              );
            }
          }
          // callCommonHandleNotificationfunction(remoteMessage);
        }
      });

    // Get the device token
    messaging()
      .getToken()
      .then(token => {
        console.log('FCM Token', token);
        return storeFCMToken(token);
      });

    // Listen to whether the token changes
    return messaging().onTokenRefresh(token => {
      storeFCMToken(token);
    });
  }, []);
  useEffect(() => {
    const unsubscribe = messaging().onMessage(async remoteMessage => {
      const { data } = remoteMessage;
      if (!isEmpty(remoteMessage?.data?.personaStatus)) {
        store.dispatch(
          AuthActionsAction.setUserData({
            ...store.getState().auth.userData,
            personaStatus: remoteMessage?.data?.personaStatus,
          }) as any,
        );
      }

      if (remoteMessage?.data?.action === 'update_bank_profile') {
        store.dispatch(
          AuthActionsAction.setUserData({
            ...store.getState().auth.userData,
            bankAccountVerified: data?.data?.bankAccountVerified,
          }),
        );
      }
      if (remoteMessage?.data?.action === 'update_availability') {
        store.dispatch(
          AuthActionsAction.setUserData({
            ...store.getState().auth.userData,
            isAvailable: data?.data?.isAvailable,
          }),
        );
      }
      if (remoteMessage?.data?.action === 'payment') {
        const currentRoute = navigationRef?.current?.getCurrentRoute();
        console.log('currentRoute ===>', currentRoute);

        // Close payment processing modal
        store.dispatch(UserConfigActions.setPaymentProcessing(false));

        // Update job data if on relevant screens
        if (
          currentRoute?.name === 'JobApplicant' ||
          currentRoute?.name === 'ChatDetails' ||
          currentRoute?.name === 'JobDetailScreen' ||
          currentRoute?.name === 'chat'
        ) {
          store.dispatch(UserConfigActions.setUpdateJobData(true));
        }
      }
      // handleNotification(remoteMessage);
      // dispatch({ type: 'updateNotification', payload: remoteMessage });
      // store.dispatch(NotificationAction.updateNotification(remoteMessage));
      // store.dispatch(
      //   NotificationAction.setBadgeCount(remoteMessage?.data?.badge),
      // );
      console.log('onMessage ===>', remoteMessage);


      // store.dispatch(NotificationAction.setNotificationType(remoteMessage));

      const parseAction = (action: any) => {
        if (typeof action === 'string') {
          try {
            // Attempt to parse JSON if it's a valid stringified object
            const parsed = JSON.parse(action);
            return typeof parsed === 'object' ? parsed : action;
          } catch (error) {
            // If parsing fails, return the original action as a string
            return action;
          }
        }
        return action; // Return as-is if it's already an object
      };

      const rawAction = remoteMessage?.data?.action;
      const action = parseAction(rawAction);


      if (typeof remoteMessage?.data?.meta === 'string') {
        const currentRoute = navigationRef?.current?.getCurrentRoute();
        let d: any = {};
        try {
          // Attempt to parse JSON if it's a valid stringified object
          const parsed = JSON.parse(remoteMessage?.data?.meta);
          console.log('parsed ===>', parsed, typeof parsed === 'object');
          d =  typeof parsed === 'object' ? parsed : remoteMessage?.data?.meta;
        } catch (error) {
          // If parsing fails, return the original action as a string
          d = remoteMessage?.data?.meta;
        }
        console.log('remoteMessaged ===>', d);
        if (
          currentRoute?.name === 'JobApplicant' && d?.jobId
        ) {
          EventRegister.emit('job_update', d);
        }
      }

      // Handle specific action cases
      if (
        action?.action === 'reward' ||
        action === 'reward' ||
        action?.action === 'streakNotification'
      ) {
        // EventRegister.emit('reward_Update', remoteMessage);
      }

      const d =
        typeof remoteMessage?.data?.meta === 'string'
          ? JSON.parse(remoteMessage?.data?.meta)
          : remoteMessage?.data?.meta;

      if (action?.action === 'reward') {
        store.dispatch(
          AuthActionsAction.setUserData({
            ...store.getState().auth.userData,
            currentBadgeName: d?.currentBadge,
          }),
        );
      }
      // ✅ Get current active route
      const currentRoute = navigationRef?.current?.getCurrentRoute();
      const isActiveChatRoom = d?.roomId === selectedRoom?.id;
      const isChatScreenActive =
        currentRoute?.name === 'ChatDetails' &&
        isActiveChatRoom &&
        remoteMessage?.data?.action === 'chat';
      if (!isChatScreenActive) {
        showNotification(remoteMessage, true);
      } else {
        //'🔕 Notification skipped - User is in the same chat'
      }
    });
    return unsubscribe;
  }, [socket, userData]);
  return null;
};

export default RemotePushNotification;
