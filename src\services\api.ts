import axios, {AxiosError, AxiosRequestConfig} from 'axios';
import NetInfo from '@react-native-community/netinfo';
import {MMKV} from 'react-native-mmkv';
import {jwtDecode} from 'jwt-decode';
import {logError} from '@/utils/sentryUtils';
import {useAuthStore} from '@/store/authStore';

// Create storage instance for tokens
export const tokenStorage = new MMKV({
  id: 'auth-tokens',
  encryptionKey: 'your-encryption-key', // Replace with a secure key
});

// Function to get email from auth store
const getEmailFromStore = () => {
  const store = useAuthStore.getState();
  return store.user?.email;
};

export const clearTokenStorage = () => {
  tokenStorage.clearAll();
};
// API base URL
export const API_BASE_URL =
  'https://873d-2409-40c1-501c-51f9-148c-e29d-4a54-3209.ngrok-free.app/go-reqt/v1';

// Create axios instance
const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 15000, // 15 seconds
  headers: {
    'Content-Type': 'application/json',
    Accept: 'application/json',
  },
  validateStatus: () => true, // Always resolve promises (even for error status)
});

// === JWT Utility Functions ===

interface JwtPayload {
  exp: number; // expiration time (seconds)
  iat?: number;
  [key: string]: any;
}

const isTokenExpiringSoon = (token: string, thresholdInSeconds = 60): boolean => {
  try {
    const decoded = jwtDecode<JwtPayload>(token);
    const currentTime = Math.floor(Date.now() / 1000);
    return decoded.exp - currentTime < thresholdInSeconds;
  } catch {
    return true; // If token is invalid or can't decode, treat as expired
  }
};

// === Axios Interceptors ===

api.interceptors.request.use(
  async config => {
    // Check internet connection
    const netInfo = await NetInfo.fetch();
    if (!netInfo.isConnected) {
      return Promise.reject(new Error('No internet connection'));
    }

    let accessToken = tokenStorage.getString('accessToken');
    const refresh_token = tokenStorage.getString('refreshToken');
    const email = getEmailFromStore();

    // If access token is about to expire, refresh it
    if (accessToken && isTokenExpiringSoon(accessToken, 60)) {
      try {
        const response = await axios.post(
          `${API_BASE_URL}/refresh-token`,
          {refresh_token, email},
          {headers: {'Content-Type': 'application/json'}},
        );

        const {accessToken: newAccessToken, refreshToken: newRefreshToken} = response.data.data;

        // Save new tokens
        tokenStorage.set('accessToken', newAccessToken);
        tokenStorage.set('refreshToken', newRefreshToken);
        accessToken = newAccessToken;
      } catch (refreshError) {
        return Promise.reject(refreshError);
      }
    }

    // Attach access token to headers
    if (accessToken && config.headers) {
      config.headers.Authorization = `Bearer ${accessToken}`;
    }

    return config;
  },
  error => Promise.reject(error),
);

api.interceptors.response.use(
  response => response,
  async (error: AxiosError) => {
    const originalRequest = error.config as AxiosRequestConfig & {_retry?: boolean};

    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;

      const refresh_token = tokenStorage.getString('refreshToken');
      const email = getEmailFromStore();

      if (!refresh_token) {
        return Promise.reject(error);
      }
      try {
        const response = await axios.post(
          `${API_BASE_URL}/refresh-token`,
          {refresh_token, email},
          {headers: {'Content-Type': 'application/json'}},
        );

        const {accessToken: newAccessToken, refreshToken: newRefreshToken} = response.data;

        // Save new tokens
        tokenStorage.set('accessToken', newAccessToken);
        tokenStorage.set('refreshToken', newRefreshToken);

        // Retry original request with new token
        if (!originalRequest.headers) {
          originalRequest.headers = {};
        }
        originalRequest.headers.Authorization = `Bearer ${newAccessToken}`;

        return api(originalRequest);
      } catch (refreshError) {
        return Promise.reject(refreshError);
      }
    }

    return Promise.reject(error);
  },
);

// === Helper function to handle API errors ===

export const handleApiError = (error: unknown, context: string = 'api'): string => {
  if (axios.isAxiosError(error)) {
    const axiosError = error as AxiosError;

    if (!axiosError.response) {
      return 'Network error. Please check your connection.';
    }

    const status = axiosError.response.status;
    let errorMessage = '';

    switch (status) {
      case 400:
        errorMessage = 'Invalid request. Please check your data.';
        break;
      case 401:
        errorMessage = 'Unauthorized. Please login again.';
        break;
      case 403:
        errorMessage = 'You do not have permission to access this resource.';
        break;
      case 404:
        errorMessage = 'The requested resource was not found.';
        break;
      case 500:
        errorMessage = 'Server error. Please try again later.';
        break;
      default:
        errorMessage = `Error: ${axiosError.message}`;
    }

    // Don't report 401 errors to Sentry as they're usually just expired tokens
    if (status !== 401) {
      // Sentry reporting is handled by the enhanceApiWithSentry interceptors
    }

    logError(error as Error, {
      context: 'ApiError',
      action: 'handleApiError',
      message: errorMessage,
    });
    return errorMessage;
  }

  logError(error as Error, {
    context: 'ApiError',
    action: 'handleApiError',
  });
  if (error instanceof Error) {
    // For non-Axios errors, we should report them to Sentry
    // Sentry reporting is handled by the enhanceApiWithSentry interceptors
    return error.message;
  }

  return 'An unknown error occurred.';
};

export default api;
