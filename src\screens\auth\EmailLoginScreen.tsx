import React, {useRef} from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  TextInput,
} from 'react-native';
import {useNavigation} from '@react-navigation/native';
import {StackNavigationProp} from '@react-navigation/stack';
import {RootStackParamList} from '@navigation/index';
import {useThemeStore} from '@/store/themeStore';
import {useForm, Controller} from 'react-hook-form';
import {yupResolver} from '@hookform/resolvers/yup';
import * as yup from 'yup';
import {CInput, SafeAreaView, CButton} from '@components/index';
import {useAuthStore} from '@/store';
import {useLogin} from '@/hooks/queries/useAuth';
import {toaster} from '@/utils/commonFunctions';

type EmailLoginScreenNavigationProp = StackNavigationProp<RootStackParamList, 'EmailLogin'>;

// Form validation schema
const schema = yup.object({
  email: yup.string().email('Email format is invalid').required('Email is required'),
  password: yup
    .string()
    .min(6, 'Password must be at least 6 characters')
    .required('Password is required'),
});

// Form data type
type FormData = yup.InferType<typeof schema>;

const EmailLoginScreen = ({route}: {route: any}) => {
  const navigation = useNavigation<EmailLoginScreenNavigationProp>();
  const theme = useThemeStore();
  const {email} = route.params;

  const {isApiStatus} = useAuthStore();

  // Use React Query for login
  const loginMutation = useLogin();

  const styles = StyleSheet.create({
    container: {
      flex: 1,
    },
    keyboardAvoidingView: {
      flex: 1,
    },
    scrollContainer: {
      flexGrow: 1,
      padding: 20,
      justifyContent: 'center',
    },
    headerContainer: {
      marginBottom: 30,
      alignItems: 'center',
    },
    title: {
      fontSize: 24,
      color: theme.colors.white,
      textAlign: 'center',
    },
    form: {
      marginBottom: 20,
    },
    inputContainer: {
      marginBottom: 12,
    },
    label: {
      marginBottom: 8,
      fontWeight: '500',
      color: theme.colors.white,
      fontSize: 14,
    },
    input: {
      height: 50,
      paddingHorizontal: 16,
      borderRadius: 8,
      borderWidth: 1,
    },
    errorText: {
      color: theme.colors.coralRed,
      fontSize: 12,
      marginTop: 4,
    },
    forgotPasswordContainer: {
      alignSelf: 'flex-end',
      marginBottom: 24,
      padding: 2,
    },
    forgotPasswordText: {
      color: theme.colors.link,
      fontSize: 12,
    },
    nextButton: {
      padding: 14,
      borderRadius: 8,
      alignItems: 'center',
    },
    nextButtonText: {
      color: theme.colors.white,
      fontSize: 16,
      fontWeight: '600',
    },
  });

  // Add refs for the input fields
  const emailInputRef = useRef<TextInput>(null);
  const passwordInputRef = useRef<TextInput>(null);

  // Initialize form with react-hook-form
  const {
    control,
    handleSubmit,
    formState: {errors},
  } = useForm<FormData>({
    resolver: yupResolver(schema),
    defaultValues: {
      email: email || '',
      password: '',
    },
  });

  const onSubmit = (data: FormData) => {
    // Use React Query mutation for login
    if (isApiStatus) {
      loginMutation.mutate(
        {
          email: data.email,
          password: data.password,
        },
        {
          onSuccess: e => {
            // Navigate to main tabs on successful login
            toaster('success', e.message, 'top');
            navigation.navigate('MainTabs');
          },
          onError: error => {
            // Error is already handled by the mutation
            toaster('error', error.message, 'top');
            console.log('error ===>', error);
          },
        },
      );
    } else {
      navigation.navigate('MainTabs');
    }
  };

  const handleForgotPassword = () => {
    // Navigate to the forgot password screen
    navigation.navigate('ForgotPassword');
  };

  return (
    <SafeAreaView
      // includeBottom={false}
      style={[styles.container, {backgroundColor: theme.colors.background}]}>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.keyboardAvoidingView}>
        <ScrollView contentContainerStyle={styles.scrollContainer}>
          <View style={styles.headerContainer}>
            <Text style={[styles.title, {color: theme.colors.white}]}>Welcome back</Text>
          </View>

          <View style={styles.form}>
            <View style={styles.inputContainer}>
              <Controller
                control={control}
                name="email"
                render={({field: {onChange, onBlur, value}}) => (
                  <CInput
                    label="Email"
                    showLabel={true}
                    placeholder="Enter your email"
                    placeholderTextColor={theme.colors.placeholder}
                    value={value}
                    onChangeText={onChange}
                    onBlur={onBlur}
                    hasError={!!errors.email}
                    error={errors.email?.message}
                    keyboardType="email-address"
                    autoCapitalize="none"
                    inputStyle={styles.input}
                    containerStyle={{marginBottom: 0}}
                    ref={emailInputRef}
                    returnKeyType="next"
                    onSubmitEditing={() => passwordInputRef.current?.focus()}
                    blurOnSubmit={false}
                  />
                )}
              />
            </View>

            <View style={styles.inputContainer}>
              <Controller
                control={control}
                name="password"
                render={({field: {onChange, onBlur, value}}) => (
                  <CInput
                    label="Password"
                    showLabel={true}
                    placeholder="Enter your password"
                    placeholderTextColor={theme.colors.placeholder}
                    value={value}
                    onChangeText={onChange}
                    onBlur={onBlur}
                    hasError={!!errors.password}
                    error={errors.password?.message}
                    secureTextEntry
                    inputStyle={styles.input}
                    containerStyle={{marginBottom: 0}}
                    ref={passwordInputRef}
                    returnKeyType="done"
                    onSubmitEditing={handleSubmit(onSubmit)}
                    blurOnSubmit={false}
                  />
                )}
              />
            </View>

            <TouchableOpacity
              style={styles.forgotPasswordContainer}
              onPress={handleForgotPassword}
              activeOpacity={0.7}
              hitSlop={{top: 8, right: 8, bottom: 8, left: 8}}>
              <Text style={styles.forgotPasswordText}>Forgot your password?</Text>
            </TouchableOpacity>

            <CButton
              title="Login"
              onPress={handleSubmit(onSubmit)}
              loading={loginMutation.isPending}
              isDisabled={loginMutation.isPending}
            />
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

export default EmailLoginScreen;
