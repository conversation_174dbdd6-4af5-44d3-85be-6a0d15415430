import React, {useEffect, useRef, useState} from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  TextInput,
} from 'react-native';
import {useNavigation} from '@react-navigation/native';
import {StackNavigationProp} from '@react-navigation/stack';
import {RootStackParamList} from '@navigation/index';
import {useThemeStore} from '@/store/themeStore';
import {useForm, Controller} from 'react-hook-form';
import {yupResolver} from '@hookform/resolvers/yup';
import * as yup from 'yup';
import {CInput, Icon, SafeAreaView} from '@/components';
import Typography from '@/components/Typography';
import {getGlobalStyles} from '@/utils';
import {useAuthStore} from '@/store';
import {useForgotPassword, useResendOtp, useResetPassword} from '@/hooks/queries/useAuth';
import {toaster, validatePassword} from '@/utils/commonFunctions';
import {OtpInput} from 'react-native-otp-entry';
import {OTP_SCREEN_TIMEOUT} from './Verification';
import useTranslation from '@/hooks/useTranslation';

type ForgotPasswordScreenNavigationProp = StackNavigationProp<RootStackParamList, 'ForgotPassword'>;

// Form data type
type FormData = yup.InferType<typeof schema>;

const ForgotPasswordScreen = ({route}: {route: {params: {email?: string}}}) => {
  const email = route?.params?.email ?? '';
  const navigation = useNavigation<ForgotPasswordScreenNavigationProp>();
  const theme = useThemeStore();
  const {t} = useTranslation();

  const emailInputRef = useRef<TextInput>(null);
  const passwordInputRef = useRef<TextInput>(null);
  const confirmPasswordInputRef = useRef<TextInput>(null);
  const otpRef = useRef<any>(null);

  const globalStyles = getGlobalStyles({theme});
  const {isApiStatus} = useAuthStore();
  const forgotPasswordMutation = useForgotPassword();
  const resetPasswordApi = useResetPassword();
  const resendOtpApi = useResendOtp();

  const [isSuccess, setIsSuccess] = React.useState<boolean>(false);
  const mutation = isSuccess ? resetPasswordApi : forgotPasswordMutation;

  const [timer, setTimer] = useState(OTP_SCREEN_TIMEOUT);
  const [isResendDisabled, setIsResendDisabled] = useState(true);

  useEffect(() => {
    let interval: NodeJS.Timeout | null = null;
    if (isResendDisabled) {
      interval = setInterval(() => {
        setTimer(prev => {
          if (prev <= 1) {
            clearInterval(interval!);
            setIsResendDisabled(false);
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
    }
    return () => {
      if (interval) clearInterval(interval);
    };
  }, [isResendDisabled]);

  // Form validation schema
  const schema = yup.object({
    email: yup
      .string()
      .email(t('forgotPasswordScreen.emailFormatInvalid'))
      .required(t('forgotPasswordScreen.emailRequired')),
    password: isSuccess
      ? yup
          .string()
          .required(t('forgotPasswordScreen.passwordRequired'))
          .test(
            'password-validation',
            t('forgotPasswordScreen.passwordValidation'),
            value => !!value && validatePassword(value).isValid,
          )
      : yup.string(),
    confirmPassword: isSuccess
      ? yup
          .string()
          .oneOf([yup.ref('password')], t('forgotPasswordScreen.passwordsMustMatch'))
          .required(t('forgotPasswordScreen.pleaseConfirmYourPassword'))
      : yup.string(),
    code: isSuccess
      ? yup
          .string()
          .length(6, t('forgotPasswordScreen.codeMustBe6Digits'))
          .required(t('forgotPasswordScreen.codeRequired'))
      : yup.string(),
  });

  const {
    control,
    handleSubmit,
    formState: {errors},
  } = useForm<FormData>({
    resolver: yupResolver(schema),
    defaultValues: {
      email: email || '',
      password: '',
    },
  });

  const handleBack = () => {
    navigation.goBack();
  };

  const onSubmit = (_data: FormData) => {
    const payload = isSuccess
      ? {email: _data.email, password: _data.password, code: _data.code}
      : {email: _data.email};
    if (isApiStatus) {
      mutation.mutate(payload as any, {
        onSuccess: response => {
          if (response?.status) {
            if (isSuccess) {
              toaster('success', response.message, 'top');
              navigation.goBack();
            } else {
              setIsSuccess(true);
            }
          } else {
            toaster('error', response.message, 'top');
          }
        },
        onError: error => {
          toaster('error', error.message, 'top');
        },
      });
    } else {
      navigation.navigate('Verification', {
        email: _data.email,
      });
    }
  };

  const resendOtp = () => {
    const emailStr = control._formValues.email;

    if (isApiStatus) {
      resendOtpApi.mutate(
        {email: emailStr},
        {
          onSuccess: response => {
            if (response?.status) {
              setIsResendDisabled(true);
              setTimer(OTP_SCREEN_TIMEOUT);
              toaster('success', response?.message, 'top');
            } else {
              toaster('error', response?.message, 'top');
            }
          },
          onError: error => {
            toaster('error', error.message, 'top');
          },
        },
      );
    }
  };

  const formatTime = (seconds: number) => {
    const m = Math.floor(seconds / 60);
    const s = seconds % 60;
    return `${m.toString().padStart(2, '0')}:${s.toString().padStart(2, '0')}`;
  };

  console.log(errors.code);
  return (
    <SafeAreaView
      includeTop
      style={[styles(theme).container, {backgroundColor: theme.colors.background}]}>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles(theme).keyboardAvoidingView}>
        <ScrollView
          contentContainerStyle={styles(theme).scrollContainer}
          keyboardShouldPersistTaps="handled">
          <View style={styles(theme).headerContainer}>
            <TouchableOpacity
              onPress={handleBack}
              style={styles(theme).backButton}
              activeOpacity={0.7}
              hitSlop={{top: 10, right: 10, bottom: 10, left: 10}}>
              <Icon name="Left-chevron" size={22} color={theme.colors.gray} />
            </TouchableOpacity>
            <Typography variant="subtitle" style={globalStyles.title}>
              {t('forgotPasswordScreen.title')}
            </Typography>
          </View>

          <View style={styles(theme).form}>
            <Text style={styles(theme).instructions}>{t('forgotPasswordScreen.instructions')}</Text>

            <View style={styles(theme).inputContainer}>
              <Controller
                control={control}
                name="email"
                render={({field: {onChange, onBlur, value}}) => (
                  <CInput
                    label={t('forgotPasswordScreen.email')}
                    showLabel={true}
                    variant="dark"
                    placeholder={t('forgotPasswordScreen.emailPlaceholder')}
                    value={value}
                    onChangeText={onChange}
                    onBlur={onBlur}
                    hasError={!!errors.email}
                    error={errors.email?.message}
                    keyboardType="email-address"
                    autoCapitalize="none"
                    inputStyle={styles(theme).input}
                    containerStyle={{marginBottom: 0}}
                    returnKeyType="done"
                    onSubmitEditing={handleSubmit(onSubmit)}
                    blurOnSubmit={false}
                    ref={emailInputRef}
                  />
                )}
              />
            </View>
            {isSuccess && (
              <>
                <View style={styles(theme).inputContainer}>
                  <Controller
                    control={control}
                    name="password"
                    render={({field: {onChange, onBlur, value}}) => {
                      const validation = validatePassword(value);

                      const errorMessage =
                        errors.password?.message ||
                        (value.length > 0 && !validation.isValid ? validation.message : '');

                      return (
                        <CInput
                          label={t('forgotPasswordScreen.newPassword')}
                          showLabel={true}
                          variant="dark"
                          placeholder={t('forgotPasswordScreen.newPasswordPlaceholder')}
                          value={value}
                          onChangeText={onChange}
                          onBlur={onBlur}
                          hasError={!!errors.password || (value.length > 0 && !validation.isValid)}
                          error={errorMessage}
                          secureTextEntry
                          inputStyle={styles(theme).input}
                          containerStyle={{marginBottom: 0}}
                          ref={passwordInputRef}
                          returnKeyType="done"
                          onSubmitEditing={() => confirmPasswordInputRef.current?.focus()}
                        />
                      );
                    }}
                  />
                </View>
                <View style={styles(theme).inputContainer}>
                  <Controller
                    control={control}
                    name="confirmPassword"
                    render={({field: {onChange, onBlur, value}}) => (
                      <CInput
                        label={t('forgotPasswordScreen.confirmPassword')}
                        showLabel={true}
                        variant="dark"
                        placeholder={t('forgotPasswordScreen.confirmPasswordPlaceholder')}
                        value={value}
                        onChangeText={onChange}
                        onBlur={onBlur}
                        hasError={!!errors.confirmPassword}
                        error={errors.confirmPassword?.message}
                        secureTextEntry
                        inputStyle={styles(theme).input}
                        containerStyle={{marginBottom: 0}}
                        ref={confirmPasswordInputRef}
                        returnKeyType="done"
                        blurOnSubmit={false}
                        onSubmitEditing={() => otpRef.current?.focus()}
                      />
                    )}
                  />
                </View>
                <View style={styles(theme).inputContainer}>
                  <Typography style={styles(theme).otpLabel}>
                    {t('forgotPasswordScreen.code')}
                  </Typography>
                  <Controller
                    control={control}
                    name="code"
                    render={({field: {onChange, value}}) => (
                      <OtpInput
                        ref={otpRef}
                        numberOfDigits={6}
                        autoFocus={true}
                        focusColor={theme.colors.activeColor}
                        blurOnFilled={true}
                        disabled={false}
                        type="numeric"
                        secureTextEntry={false}
                        focusStickBlinkingDuration={500}
                        onTextChange={onChange}
                        onFilled={handleSubmit(onSubmit)}
                        theme={{
                          pinCodeContainerStyle: {
                            borderColor: errors.code
                              ? theme.colors.coralRed
                              : theme.colors.activeColor,
                            width: 50,
                          },
                          pinCodeTextStyle: {color: theme.colors.activeColor},
                        }}
                      />
                    )}
                  />
                  {errors.code && (
                    <Typography variant="body" style={styles(theme).errorText}>
                      {errors.code.message}
                    </Typography>
                  )}
                </View>

                {/* Resend */}
                <TouchableOpacity
                  onPress={() => {
                    if (timer === 0) resendOtp();
                  }}
                  disabled={isResendDisabled}
                  style={styles(theme).resendContainer}>
                  <Text
                    style={{
                      color: isResendDisabled ? theme.colors.white2 : theme.colors.primary,
                      fontSize: theme.fontSize.medium,
                    }}>
                    {isResendDisabled
                      ? `${t('forgotPasswordScreen.resendCodeIn')} ${formatTime(timer)}`
                      : t('forgotPasswordScreen.resendCode')}
                  </Text>
                </TouchableOpacity>
              </>
            )}

            <TouchableOpacity
              style={[
                styles(theme).resetButton,
                {backgroundColor: theme.colors.primary},
                mutation.isPending && styles(theme).disabledButton,
              ]}
              onPress={handleSubmit(onSubmit)}
              disabled={mutation.isPending}
              activeOpacity={0.7}>
              <Text style={styles(theme).resetButtonText}>
                {mutation.isPending
                  ? t('forgotPasswordScreen.sending')
                  : t('forgotPasswordScreen.resetPassword')}
              </Text>
            </TouchableOpacity>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = (theme: any) =>
  StyleSheet.create({
    container: {
      flex: 1,
    },
    keyboardAvoidingView: {
      flex: 1,
    },
    scrollContainer: {
      flexGrow: 1,
      paddingHorizontal: 20,
    },
    headerContainer: {
      flexDirection: 'row',
      marginBottom: 30,
      position: 'relative',
      alignItems: 'center',
      justifyContent: 'flex-start',
      paddingTop: 20,

      gap: 10,
    },
    backButton: {
      padding: 5,
    },
    title: {
      fontSize: theme.fontSize.xlarge,
      color: theme.colors.white,
    },
    form: {
      marginBottom: 20,
    },
    instructions: {
      color: theme.colors.transparentWhite1,
      marginBottom: 24,
      textAlign: 'center',
      lineHeight: 20,
    },
    inputContainer: {
      marginBottom: 24,
    },
    label: {
      marginBottom: 8,
      fontWeight: '500',
      color: theme.colors.white,
      fontSize: theme.fontSize.font14,
    },
    input: {
      height: 50,
      paddingHorizontal: 16,
      borderRadius: 8,
      borderWidth: 1,
    },
    resetButton: {
      padding: 14,
      borderRadius: 8,
      alignItems: 'center',
    },
    resetButtonText: {
      color: theme.colors.white,
      fontSize: theme.fontSize.medium,
      fontWeight: '600',
    },
    disabledButton: {
      opacity: 0.7,
    },
    otpLabel: {
      marginBottom: 6,
      fontSize: theme.fontSize.font14,
      fontWeight: '500',
      color: theme.colors.activeColor,
    },
    resendContainer: {
      alignSelf: 'flex-end',
      marginBottom: 15,
      padding: 2,
    },
    errorText: {
      color: theme.colors.coralRed,
      marginTop: 5,
      fontSize: theme.fontSize.small,
      marginBottom: -15,
    },
  });

export default ForgotPasswordScreen;
