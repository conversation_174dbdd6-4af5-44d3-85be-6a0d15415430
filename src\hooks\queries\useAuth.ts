import {useMutation, useQuery, useQueryClient} from '@tanstack/react-query';
import {
  loginWithEmailApi,
  registerWithEmailApi,
  logoutApi,
  checkAuthStatusApi,
  getCurrentUserApi,
  LoginRequest,
  RegisterRequest,
  otpVerifyApi,
  OtpVerifyRequest,
  otpResendApi,
  OtpResendRequest,
  forgotPasswordApi,
  ForgotPasswordRequest,
  ResetPasswordRequest,
  resetPasswordApi,
  socialLoginApi,
  SocialRegisterRequest,
} from '@/services/authApi';
import {useAuthStore, User} from '@/store/authStore';
import {toaster} from '@/utils/commonFunctions';
import {useConfigStore} from '@/store';

// Query keys
export const authKeys = {
  all: ['auth'] as const,
  user: () => [...authKeys.all, 'user'] as const,
  status: () => [...authKeys.all, 'status'] as const,
};

/**
 * Hook for logging in with email and password
 */
export const useLogin = () => {
  const queryClient = useQueryClient();
  const {login, loginStart, loginFailure} = useAuthStore();
  const {setProfileSetup} = useConfigStore();

  return useMutation({
    mutationFn: async (credentials: LoginRequest) => {
      loginStart();
      return loginWithEmailApi(credentials);
    },
    onSuccess: data => {
      console.log('data=====>>>>>', data);
      if (data.data?.user_not_verified === true) {
        return data;
      } else {
        // Update auth store
        if (data.data?.is_profile_completed === true) {
          setProfileSetup(true);
        }
        login(data.data as User);
        // Invalidate relevant queries
        queryClient.invalidateQueries({queryKey: authKeys.status()});
        queryClient.invalidateQueries({queryKey: authKeys.user()});
        // Set user data in the query cache
        queryClient.setQueryData(authKeys.user(), data.user);
      }
      toaster('success', data.message, 'top');
    },
    onError: (error: Error) => {
      toaster('error', error.message, 'top');
      loginFailure(error.message);
    },
  });
};

/**
 * Hook for logging in with email and password
 */
export const useSocialLogin = () => {
  const queryClient = useQueryClient();
  const {login, loginStart, loginFailure} = useAuthStore();
  const {setProfileSetup} = useConfigStore();

  return useMutation({
    mutationFn: async (credentials: SocialRegisterRequest) => {
      return socialLoginApi(credentials);
    },
    onSuccess: data => {
      if (data?.status) {
        console.log('socialLoginApi =====>>>>>', data);
        // Update auth store
        if (data.data?.is_profile_completed === true) {
          setProfileSetup(true);
        }
        login(data.data as User);
        // Invalidate relevant queries
        queryClient.invalidateQueries({queryKey: authKeys.status()});
        queryClient.invalidateQueries({queryKey: authKeys.user()});
        // Set user data in the query cache
        queryClient.setQueryData(authKeys.user(), data.data);
        toaster('success', data.message, 'top');
      } else {
        toaster('error', data?.message, 'top');
        loginFailure(data?.message);
      }
    },
    onError: (error: Error) => {
      toaster('error', error.message, 'top');
      loginFailure(error.message);
    },
  });
};

/**
 * Hook for registering with email, password, and name
 */
export const useRegister = () => {
  const queryClient = useQueryClient();
  const {login, loginStart, loginFailure} = useAuthStore();

  return useMutation({
    mutationFn: async (userData: RegisterRequest) => {
      loginStart();
      return registerWithEmailApi(userData);
    },
    onSuccess: data => {
      return data;
      // Update auth store
      // login(data.data as User);

      // // Invalidate relevant queries
      // queryClient.invalidateQueries({queryKey: authKeys.status()});
      // queryClient.invalidateQueries({queryKey: authKeys.user()});

      // // Set user data in the query cache
      // queryClient.setQueryData(authKeys.user(), data.user);
    },
    onError: (error: Error) => {
      loginFailure(error.message);
    },
  });
};

export const useVerify = () => {
  const {login, loginStart, loginFailure} = useAuthStore();

  return useMutation({
    mutationFn: async (data: OtpVerifyRequest) => {
      loginStart();
      return otpVerifyApi(data);
    },
    onSuccess: data => {
      return data;
    },
    onError: (error: Error) => {
      loginFailure(error.message);
    },
  });
};

export const useResetPassword = () => {
  const {login, loginStart, loginFailure} = useAuthStore();

  return useMutation({
    mutationFn: async (data: ResetPasswordRequest) => {
      loginStart();
      return resetPasswordApi(data);
    },
    onSuccess: data => {
      return data;
    },
    onError: (error: Error) => {
      loginFailure(error.message);
    },
  });
};

export const useResendOtp = () => {
  const {login, loginStart, loginFailure} = useAuthStore();

  return useMutation({
    mutationFn: async (data: OtpResendRequest) => {
      loginStart();
      return otpResendApi(data);
    },
    onSuccess: data => {
      return data;
    },
    onError: (error: Error) => {
      loginFailure(error.message);
    },
  });
};

export const useForgotPassword = () => {
  const {login, loginStart, loginFailure} = useAuthStore();

  return useMutation({
    mutationFn: async (data: ForgotPasswordRequest) => {
      loginStart();
      return forgotPasswordApi(data);
    },
    onSuccess: data => {
      return data;
    },
    onError: (error: Error) => {
      loginFailure(error.message);
    },
  });
};

/**
 * Hook for logging out
 */
export const useLogout = () => {
  const queryClient = useQueryClient();
  const {logout} = useAuthStore();

  return useMutation({
    mutationFn: logoutApi,
    onSuccess: () => {
      // Update auth store
      logout();

      // Invalidate all queries to ensure fresh data on re-login
      queryClient.invalidateQueries();

      // Explicitly clear specific caches
      queryClient.invalidateQueries({queryKey: authKeys.status()});
      queryClient.invalidateQueries({queryKey: authKeys.user()});

      // Clear user data from the query cache
      queryClient.setQueryData(authKeys.user(), null);

      // Reset query client to initial state
      queryClient.resetQueries();

      console.log('Auth state completely reset');
    },
  });
};

/**
 * Hook for checking authentication status
 */
export const useAuthStatus = () => {
  const {isAuthenticated} = useAuthStore();

  return useQuery({
    queryKey: authKeys.status(),
    queryFn: checkAuthStatusApi,
    initialData: isAuthenticated, // Use the current auth state as initial data
  });
};

/**
 * Hook for getting the current user
 */
export const useCurrentUser = () => {
  const {user} = useAuthStore();

  return useQuery({
    queryKey: authKeys.user(),
    queryFn: getCurrentUserApi,
    initialData: user || undefined, // Use the current user as initial data
    enabled: !!user, // Only run if there's a user in the store
  });
};
