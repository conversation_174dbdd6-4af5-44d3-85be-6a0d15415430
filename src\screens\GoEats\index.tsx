import React, {useState} from 'react';
import {FlatList, View, TouchableOpacity, ImageBackground, RefreshControl} from 'react-native';
import {styles as createStyles} from './styles';
import {useThemeStore} from '@/store/themeStore';
import {Header, Icon, NoData, SafeAreaView} from '@/components';
import {useNavigation} from '@react-navigation/native';
import {StackNavigationProp} from '@react-navigation/stack';
import {CommunityStackParamList} from '@/navigation/CommunityStack';
import Tabs from '@/components/Tabs';
import CommunityCard from '@/components/CommunityCard';
import Typography from '@/components/Typography';
import {Images} from '@/config';
import MediaCard from '@/components/MediaCard';
import {getCommunityData, setupCommunityMutation} from '@/utils/commonFunctions';
import {useMutation, useQuery, useQueryClient} from '@tanstack/react-query';
import VideoActionsModal from '@/components/VideoActionsModal';
import CLoader from '@/components/CLoader';

type NavigationProp = StackNavigationProp<CommunityStackParamList>;

interface GoEatData {
  id: string;
  title: string;
  description: string;
  image: string;
  file_thumbnail: string;
  tags: GoFitTag[];
  buttonText?: string;
  is_liked: boolean;
  likes_count: number;
  [key: string]: any;
}

interface GoFitTag {
  id: number;
  name: string;
  title: string;
}

interface VideoModalState {
  visible: boolean;
  goEatData: Partial<GoEatData>;
}

const GoEats = () => {
  const theme = useThemeStore();
  const styles = createStyles(theme);

  const navigation = useNavigation<NavigationProp>();
  const queryClient = useQueryClient();

  const goBack = () => {
    navigation.goBack();
  };

  const tabs = ['Nutrition', 'Recipes', 'Health'];

  const [activeTab, setActiveTab] = useState('Nutrition');

  const [isVideoActionsModalVisible, setIsVideoActionsModalVisible] = useState<VideoModalState>({
    visible: false,
    goEatData: {},
  });

  const handleTabPress = (tab: string) => {
    setActiveTab(tab);
  };

  const {
    data: apiResponse,
    isLoading,
    refetch,
  } = useQuery<GoEatData[]>({
    queryKey: ['go-eats', activeTab],
    queryFn: async () => {
      const response = await getCommunityData('go-eats' as string, activeTab);
      return response || [];
    },
  });

  // Extract the data array from the API response or default to empty array
  const goEatData: GoEatData[] = apiResponse || [];

  const updateVideoModalState = (id: string, isLiked: boolean) => {
    // Update modal data if it's the same item
    if (isVideoActionsModalVisible.goEatData?.id === id) {
      setIsVideoActionsModalVisible(prev => ({
        ...prev,
        goEatData: {
          ...prev.goEatData,
          is_liked: isLiked,
          likes_count: isLiked
            ? ((prev.goEatData.likes_count as number) || 0) + 1
            : ((prev.goEatData.likes_count as number) || 0) - 1,
        },
      }));
    }
  };

  const {mutate: updateLike} = useMutation(
    setupCommunityMutation<GoEatData>(queryClient, ['go-eats', activeTab], updateVideoModalState),
  );

  const handleLikePress = (item: GoEatData) => {
    updateLike({
      id: item.id,
      is_liked: !item.is_liked,
    });
  };

  const renderComponent = ({item}: {item: GoEatData}) => {
    return (
      <View>
        <CommunityCard
          data={item}
          onCardPress={() => {
            navigation.navigate('CommunityDetails', {
              from: 'GoEats',
              title: item.title,
              data: item,
            });
          }}
          onLikePress={() => {}}
          onCommentPress={() => {}}
          onSharePress={() => {}}
          containerStyle={styles.cardContainer}
          variant="tag"
          onMoreIconPress={() => {
            setIsVideoActionsModalVisible({
              visible: true,
              goEatData: item,
            });
          }}
          onMorePress={() => {
            navigation.navigate('CommunityDetails', {
              from: 'GoEats',
              title: item.title,
              data: item,
            });
          }}
        />
        {item.buttonText && (
          <TouchableOpacity style={styles.blueButton}>
            <Typography variant="communityBtnText" color={theme.colors.white}>
              {item.buttonText}
            </Typography>
          </TouchableOpacity>
        )}
      </View>
    );
  };

  const renderHeader = () => (
    <View style={styles.headerContainer}>
      <MediaCard source={Images.goEatImage} imageContainerStyle={styles.imageContainer} />

      <View style={styles.tabContainer}>
        <Tabs
          variant="square"
          tabs={tabs}
          activeTab={activeTab}
          onTabPress={handleTabPress}
          tabStyle={styles.tabStyle}
          tabTitleStyle={styles.tabTitleStyle}
          type="inner"
          listContainerStyle={styles.listContainerStyle}
        />
      </View>
    </View>
  );

  return (
    <ImageBackground source={Images.gradientBg} style={styles.backgroundImage}>
      <SafeAreaView includeBottom={false} style={styles.root}>
        <Header
          showBack={false}
          leftComponent={
            <TouchableOpacity onPress={() => navigation.goBack()}>
              <Icon name="Left-chevron" size={24} color={theme.colors.white} />
            </TouchableOpacity>
          }
          title="GoEats"
          backgroundColor="transparent"
          isBackPress={true}
          backNavigation={goBack}
          rightIcons={[
            {name: 'notification', size: 24, badge: 0},
            {name: 'chat', size: 24, badge: 14},
          ]}
        />
        {isLoading ? (
          <CLoader />
        ) : (
          <FlatList
            data={goEatData}
            showsVerticalScrollIndicator={false}
            keyExtractor={(item, index) => index.toString()}
            renderItem={renderComponent}
            contentContainerStyle={styles.content}
            onEndReachedThreshold={0.5}
            ListHeaderComponent={renderHeader}
            refreshControl={<RefreshControl refreshing={isLoading} onRefresh={() => refetch()} />}
            ListEmptyComponent={
              <NoData
                title={`No ${activeTab} Data`}
                message={`No ${activeTab.toLowerCase()} content available at the moment.`}
              />
            }
          />
        )}
      </SafeAreaView>
      <VideoActionsModal
        visible={isVideoActionsModalVisible.visible}
        onClose={() =>
          setIsVideoActionsModalVisible({
            visible: false,
            goEatData: {},
          })
        }
        data={isVideoActionsModalVisible.goEatData}
        videoTitle="Latest Reviews"
        isLiked={isVideoActionsModalVisible.goEatData?.is_liked ?? false}
        videoThumbnail="https://picsum.photos/200/300"
        onLike={() => {
          const goEatItem = isVideoActionsModalVisible.goEatData as GoEatData;
          if (goEatItem && goEatItem.id) {
            handleLikePress(goEatItem as GoEatData);
          }
        }}
        onComment={() => {
          navigation.navigate('CommentScreen', {
            from: 'Latest Reviews',
            title: 'Latest Reviews',
            data: goEatData[0],
          });
          setIsVideoActionsModalVisible({
            visible: false,
            goEatData: {},
          });
        }}
        onShare={() => {}}
      />
    </ImageBackground>
  );
};

export default GoEats;
