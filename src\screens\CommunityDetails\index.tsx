import React from 'react';
import {View, ImageBackground} from 'react-native';
import {WebView} from 'react-native-webview';
import {useThemeStore} from '@/store/themeStore';
import {Header, SafeAreaView} from '@/components';
import {DrawerActions, useNavigation, useRoute, RouteProp} from '@react-navigation/native';
import {StackNavigationProp} from '@react-navigation/stack';
import {CommunityStackParamList} from '@/navigation/CommunityStack';
import {Images} from '@/config';
import Typography from '@/components/Typography';
import {styles as createStyles} from './styles';

type NavigationProp = StackNavigationProp<CommunityStackParamList>;
type CommunityDetailsRouteProp = RouteProp<CommunityStackParamList, 'CommunityDetails'>;

// Helper to decode HTML entities
const decodeHtmlEntities = (text: string): string => {
  return text
    .replace(/&lt;/g, '<')
    .replace(/&gt;/g, '>')
    .replace(/&quot;/g, '"')
    .replace(/&apos;/g, "'")
    .replace(/&amp;/g, '&');
};

// Helper to wrap HTML content into full HTML doc
const wrapHtml = (rawHtml: string): string => {
  const decoded = decodeHtmlEntities(rawHtml);
  return `
    <!DOCTYPE html>
    <html>
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <style>
          body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen,
                         Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
            padding: 16px;
            margin: 0;
            color: #333;
            background-color: #fff;
          }
          iframe, video {
            width: 100%;
            height: auto;
            max-width: 100%;
            margin: 16px 0;
          }
          img {
            max-width: 100%;
            height: auto;
          }
          h3, h4 {
            margin-top: 24px;
          }
        </style>
      </head>
      <body>
        ${decoded}
      </body>
    </html>
  `;
};

const CommunityDetails = () => {
  const theme = useThemeStore();
  const styles = createStyles(theme);
  const route = useRoute<CommunityDetailsRouteProp>();
  const {from, title, data} = route.params;
  const navigation = useNavigation<NavigationProp>();

  const openDrawer = () => {
    navigation.dispatch(DrawerActions.openDrawer());
  };

  const htmlContent = wrapHtml(data?.description || '<p>No content available</p>');

  return (
    <ImageBackground source={Images.gradientBg} style={styles.backgroundImage}>
      <SafeAreaView includeBottom={false} style={styles.container}>
        <Header
          leftIcon={{
            name: 'side-menu',
            size: 24,
            color: theme.colors.white,
            containerStyle: theme.colors.primary,
          }}
          leftIconButtonStyle={styles.menuButton}
          onLeftPress={openDrawer}
          rightIcons={[
            {name: 'notification', size: 24, badge: 0},
            {name: 'chat', size: 24, badge: 14},
          ]}
          pageTitle={from}
          backgroundColor="transparent"
        />
        <View style={{flex: 1, flexDirection: 'column'}}>
          <View style={{flex: 1}}>
            <View style={styles.pageTitleContainer}>
              <Typography variant="pageTitle" color={theme.colors.white}>
                {title}
              </Typography>
            </View>
            <WebView
              source={{html: htmlContent}}
              style={{flex: 1}}
              originWhitelist={['*']}
              javaScriptEnabled={true}
              domStorageEnabled={true}
              allowsFullscreenVideo={true}
              mediaPlaybackRequiresUserAction={false}
              allowsInlineMediaPlayback={true}
              mixedContentMode="always"
              startInLoadingState={true}
            />
          </View>
        </View>
      </SafeAreaView>
    </ImageBackground>
  );
};

export default CommunityDetails;
