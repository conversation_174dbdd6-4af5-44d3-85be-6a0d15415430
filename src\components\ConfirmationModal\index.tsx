import React, {forwardRef, useImperativeHandle, useRef, ReactNode, useState} from 'react';
import {
  StyleSheet,
  Modal,
  View,
  TouchableWithoutFeedback,
  Animated,
  Dimensions,
  TouchableOpacity,
} from 'react-native';
import {useThemeStore} from '@/store/themeStore';

export interface ConfirmationModalProps {
  children: ReactNode;
  heightPercentage?: number;
}

export interface ConfirmationModalHandles {
  open: () => void;
  close: () => void;
}

const ConfirmationModal = forwardRef<ConfirmationModalHandles, ConfirmationModalProps>(
  ({children, heightPercentage = 25}, ref) => {
    const theme = useThemeStore();
    const [visible, setVisible] = useState(false);
    const translateY = useRef(new Animated.Value(Dimensions.get('window').height)).current;

    // Calculate modal height based on percentage
    const modalHeight = (Dimensions.get('window').height * heightPercentage) / 100;

    // Open modal with animation from bottom
    const open = () => {
      setVisible(true);
      Animated.timing(translateY, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }).start();
    };

    // Close modal with animation
    const close = () => {
      Animated.timing(translateY, {
        toValue: Dimensions.get('window').height,
        duration: 300,
        useNativeDriver: true,
      }).start(() => {
        setVisible(false);
      });
    };

    // Expose methods to parent components
    useImperativeHandle(ref, () => ({
      open,
      close,
    }));

    if (!visible) return null;

    return (
      <Modal transparent={true} visible={visible} animationType="none" onRequestClose={close}>
        <TouchableWithoutFeedback onPress={close}>
          <View style={styles(theme).backdrop}>
            <TouchableWithoutFeedback>
              <Animated.View
                style={[
                  styles(theme).modalContainer,
                  {
                    height: modalHeight,
                    transform: [{translateY}],
                  },
                ]}>
                <View style={styles(theme).contentContainer}>{children}</View>
              </Animated.View>
            </TouchableWithoutFeedback>
          </View>
        </TouchableWithoutFeedback>
      </Modal>
    );
  },
);

// Styles
const styles = (theme: any) =>
  StyleSheet.create({
    backdrop: {
      flex: 1,
      backgroundColor: 'rgba(0, 0, 0, 0.6)',
      justifyContent: 'flex-end',
    },
    modalContainer: {
      backgroundColor: theme.colors.background,
      borderTopLeftRadius: 20,
      borderTopRightRadius: 20,
      overflow: 'hidden',
    },
    contentContainer: {
      padding: 20,
      flex: 1,
    },
  });

export default ConfirmationModal;

ConfirmationModal.displayName = 'ConfirmationModal';
