import React, {useState} from 'react';
import {View, TouchableOpacity, ScrollView, ActivityIndicator, RefreshControl} from 'react-native';
import {useThemeStore} from '@/store/themeStore';
import {DrawerActions, useNavigation} from '@react-navigation/native';
import {CustomModal, Header, Icon} from '@/components';
import {OfferBanner} from '@/components/common/OfferBanner';
import {StackNavigationProp} from '@react-navigation/stack';
import {RootStackParamList} from '@/navigation';
import NotificationCard from '@/components/NotificationCard';
import {Images} from '@/config';
import {SwipeListView} from 'react-native-swipe-list-view';
import Typography from '@/components/Typography';
import {styles as createStyles} from './styles';
import {SafeAreaView} from '@/components/common';
import useTranslation from '@/hooks/useTranslation';

type NavigationProp = StackNavigationProp<RootStackParamList>;

type NotificationType = 'invite' | 'default';

interface Notification {
  id: string;
  title: string;
  type: NotificationType;
  image: any;
  highlight?: boolean;
  dimensions?: string;
  time?: string;
  community?: string;
}

const NOTIFICATIONS: Notification[] = [
  {
    id: '1',
    title: 'Player Name has invited you to play at Randalls Island, 2PM, 5/12/26  ',
    type: 'invite',
    image: Images.profile1,
    time: '16m',
    community: 'Community',
  },
  {
    id: '2',
    title: 'Tom is joining you at Tennis Park, chat now',
    type: 'default',
    image: Images.profile2,
  },
  {
    id: '3',
    title: 'Your New Balance Coco GTIs will arrive tomorrow at Central Park',
    type: 'default',
    image: Images.shoes,
  },
  {
    id: '4',
    title: 'This Saturday Dunlop Demo Day near you',
    type: 'default',
    image: Images.dunlopRound,
    highlight: true,
  },
  {
    id: '5',
    title: 'Rate your ATP Championship Tennis Balls',
    type: 'default',
    image: Images.logo,
  },
  {
    id: '6',
    title: 'Chandra messaged you to play',
    type: 'default',
    image: Images.profile3,
  },
  {
    id: '7',
    title: 'Chandra messaged you to play',
    type: 'default',
    image: Images.profile3,
  },
  {
    id: '8',
    title: 'Chandra messaged you to play',
    type: 'default',
    image: Images.profile3,
  },
  {
    id: '9',
    title: 'Chandra messaged you to play',
    type: 'default',
    image: Images.profile3,
  },
  {
    id: '10',
    title: 'Chandra messaged you to play',
    type: 'default',
    image: Images.profile3,
  },
];

interface PaginationState {
  page: number;
  loadMore: boolean;
}

const NotificationsListScreen = () => {
  const theme = useThemeStore();
  const navigation = useNavigation<NavigationProp>();
  const {t} = useTranslation();
  const [listData, setListData] = useState<Notification[]>(NOTIFICATIONS);
  const styles = createStyles(theme);
  const [openRowKey, setOpenRowKey] = useState<string | null>(null);

  const [isModalVisible, setIsModalVisible] = useState(false);

  const [paginationLoader, setPaginationLoader] = useState(false);

  const [pagination, setPagination] = useState<PaginationState>({
    page: 1,
    loadMore: false,
  });

  const [pageLoad, setPageLoad] = useState<boolean>(true);
  const [refreshLoader, setRefreshLoader] = useState<boolean>(false);

  const openDrawer = () => {
    navigation.dispatch(DrawerActions.openDrawer());
  };

  const deleteRow = (rowKey: string) => {
    const newData = [...listData];
    const prevIndex = listData.findIndex(item => item.id === rowKey);
    newData.splice(prevIndex, 1);
    setListData(newData);
  };

  const renderItem = ({item}: {item: Notification}) => (
    <View style={[styles.rowFront, openRowKey === item.id && styles.rowFrontOpen]}>
      <View style={styles.cardContainer}>
        <NotificationCard
          title={item.title}
          type={item.type}
          image={item.image}
          time={item.time}
          community={item.community}
        />
      </View>
    </View>
  );

  const renderHiddenItem = (data: {item: Notification}) => (
    <View style={styles.rowBack}>
      <TouchableOpacity
        style={[styles.actionButton, styles.moreBtn]}
        onPress={() => setIsModalVisible(true)}>
        <Icon name="threeDot" size={28} color={theme.colors.white} />
        <Typography variant="communityBtnText" style={styles.actionText}>
          {t('NotificationScreen.more')}
        </Typography>
      </TouchableOpacity>
      <TouchableOpacity
        style={[styles.actionButton, styles.trashBtn]}
        activeOpacity={0.7}
        onPress={() => {}}>
        <Icon name="delete" size={30} color={theme.colors.white} />
        <Typography variant="communityBtnText" style={styles.actionText}>
          {t('NotificationScreen.trash')}
        </Typography>
      </TouchableOpacity>
    </View>
  );

  function loadMoreData(): void {
    if (pagination?.loadMore && !paginationLoader && !pageLoad) {
      setPaginationLoader(true);
      const page = pagination?.page + 1;
    }
  }

  return (
    <SafeAreaView style={styles.container}>
      <Header
        leftComponent={
          <TouchableOpacity onPress={() => navigation.goBack()} activeOpacity={0.7}>
            <Icon name="Left-chevron" size={24} color={theme.colors.white} />
          </TouchableOpacity>
        }
        leftIconButtonStyle={styles.menuButton}
        onLeftPress={openDrawer}
        rightIcons={[{name: 'chat', size: 24, badge: 14}]}
        title={t('NotificationScreen.title')}
        backgroundColor={theme.colors.notificationBg}
        transparent={false}
        showBack={false}
      />
      <SwipeListView
        data={listData}
        renderItem={renderItem}
        renderHiddenItem={renderHiddenItem}
        rightOpenValue={-150}
        keyExtractor={item => item.id}
        contentContainerStyle={styles.listContent}
        closeOnRowPress={true}
        disableRightSwipe={true}
        showsVerticalScrollIndicator={false}
        friction={100}
        tension={100}
        swipeToOpenPercent={30}
        onEndReached={loadMoreData}
        onRowOpen={rowKey => {
          setOpenRowKey(rowKey);
        }}
        onRowClose={() => {
          setOpenRowKey(null);
        }}
        ListFooterComponent={() =>
          paginationLoader ? (
            <View style={styles.paginationLoader}>
              <ActivityIndicator size="large" color={theme.colors.primary} />
            </View>
          ) : null
        }
        refreshControl={
          <RefreshControl
            colors={[theme.colors.primary]}
            tintColor={theme.colors.primary}
            refreshing={refreshLoader}
            onRefresh={() => {
              setPagination({
                page: 1,
                loadMore: true,
              });
              setRefreshLoader(true);
            }}
          />
        }
      />
      <View style={styles.footer}>
        <OfferBanner text="Invite friends, get 10% off" />
      </View>
      <CustomModal
        visible={isModalVisible}
        variant="bottom"
        modalContainerStyle={{
          height: '100%',
          width: '100%',
        }}
        showCloseButton
        onClose={() => {
          setIsModalVisible(false);
        }}>
        <ScrollView contentContainerStyle={styles.modalContent}>
          <View style={styles.modalRow}>
            <TouchableOpacity
              style={styles.modalButton}
              onPress={() => {
                setIsModalVisible(false);
                // Add reply functionality here
              }}>
              <View style={styles.iconContainer}>
                <Icon name="reply" size={30} color={theme.colors.white} />
              </View>
              <Typography
                variant="notificationText"
                color={theme.colors.white}
                style={{
                  marginTop: 5,
                }}>
                {t('NotificationScreen.reply')}
              </Typography>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.modalButton}
              onPress={() => {
                setIsModalVisible(false);

                if (openRowKey) deleteRow(openRowKey);
              }}>
              <View style={styles.iconContainer}>
                <Icon name="delete" size={32} color={theme.colors.white} />
              </View>
              <Typography
                variant="notificationText"
                color={theme.colors.white}
                style={{
                  marginTop: 5,
                }}>
                {t('NotificationScreen.trash')}
              </Typography>
            </TouchableOpacity>
          </View>

          <TouchableOpacity
            style={styles.modalFullButton}
            onPress={() => {
              setIsModalVisible(false);
            }}>
            <Typography variant="notificationText" color={theme.colors.white}>
              {t('NotificationScreen.remindMe')}
            </Typography>
          </TouchableOpacity>

          {/* Multiple notification function buttons */}
          {[1, 2, 3, 4].map(item => (
            <TouchableOpacity
              key={`notification-function-${item}`}
              style={styles.modalFullButton}
              onPress={() => {
                setIsModalVisible(false);
                // Add notification function here
              }}>
              <Typography variant="notificationText" color={theme.colors.white}>
                {t('NotificationScreen.notificationFunction')}
              </Typography>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </CustomModal>
    </SafeAreaView>
  );
};

export default NotificationsListScreen;
